using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private PictureBox logoBox;
        private CheckBox chkRememberMe;

        public LoginForm()
        {
            InitializeComponent();
            SetupModernUI();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الأساسية
            this.Text = "نظام المحاسبة الشامل - تسجيل الدخول";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);

            // اللوحة الرئيسية
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            // لوحة تسجيل الدخول
            loginPanel = new Panel
            {
                Size = new Size(400, 500),
                BackColor = Color.FromArgb(37, 37, 38),
                Location = new Point((this.Width - 400) / 2, (this.Height - 500) / 2)
            };

            // العنوان
            lblTitle = new Label
            {
                Text = "نظام المحاسبة الشامل",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(360, 40),
                Location = new Point(20, 50)
            };

            // الشعار
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(160, 100),
                BackColor = Color.FromArgb(0, 122, 204),
                SizeMode = PictureBoxSizeMode.CenterImage
            };

            // تسمية اسم المستخدم
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(280, 200),
                TextAlign = ContentAlignment.MiddleRight
            };

            // حقل اسم المستخدم
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 12F),
                Size = new Size(300, 35),
                Location = new Point(50, 230),
                BackColor = Color.FromArgb(62, 62, 66),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.None,
                Text = "admin" // قيمة افتراضية للاختبار
            };

            // تسمية كلمة المرور
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(280, 280),
                TextAlign = ContentAlignment.MiddleRight
            };

            // حقل كلمة المرور
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 12F),
                Size = new Size(300, 35),
                Location = new Point(50, 310),
                BackColor = Color.FromArgb(62, 62, 66),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.None,
                UseSystemPasswordChar = true,
                Text = "admin123" // قيمة افتراضية للاختبار
            };

            // خانة تذكرني
            chkRememberMe = new CheckBox
            {
                Text = "تذكرني",
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.White,
                Size = new Size(100, 25),
                Location = new Point(280, 360),
                TextAlign = ContentAlignment.MiddleRight,
                CheckAlign = ContentAlignment.MiddleLeft
            };

            // زر تسجيل الدخول
            btnLogin = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                Size = new Size(300, 45),
                Location = new Point(50, 400),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            // إضافة الأحداث
            btnLogin.Click += BtnLogin_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            btnLogin.MouseEnter += BtnLogin_MouseEnter;
            btnLogin.MouseLeave += BtnLogin_MouseLeave;

            // إضافة العناصر للوحات
            loginPanel.Controls.AddRange(new Control[] {
                lblTitle, logoBox, lblUsername, txtUsername,
                lblPassword, txtPassword, chkRememberMe, btnLogin
            });

            mainPanel.Controls.Add(loginPanel);
            this.Controls.Add(mainPanel);

            this.ResumeLayout(false);
        }

        private void SetupModernUI()
        {
            // تطبيق التأثيرات البصرية
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | 
                         ControlStyles.UserPaint | 
                         ControlStyles.DoubleBuffer, true);

            // إضافة تأثير الظل للوحة تسجيل الدخول
            loginPanel.Paint += LoginPanel_Paint;
            
            // إضافة تأثيرات للحقول النصية
            txtUsername.Paint += TextBox_Paint;
            txtPassword.Paint += TextBox_Paint;

            // تخصيص الشعار
            logoBox.Paint += LogoBox_Paint;
        }

        private void LoginPanel_Paint(object sender, PaintEventArgs e)
        {
            // التحقق من أن الأبعاد ليست صفراً لتجنب خطأ Rectangle
            if (loginPanel.ClientRectangle.Width > 0 && loginPanel.ClientRectangle.Height > 0)
            {
                // رسم خلفية متدرجة
                using (var brush = new LinearGradientBrush(
                    loginPanel.ClientRectangle,
                    Color.FromArgb(37, 37, 38),
                    Color.FromArgb(45, 45, 48),
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, loginPanel.ClientRectangle);
                }

                // رسم حدود
                using (var pen = new Pen(Color.FromArgb(0, 122, 204), 2))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, loginPanel.Width - 1, loginPanel.Height - 1);
                }
            }
        }

        private void TextBox_Paint(object sender, PaintEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && textBox.Width > 0 && textBox.Height > 0)
            {
                // رسم حدود ملونة
                using (var pen = new Pen(Color.FromArgb(0, 122, 204), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, textBox.Width - 1, textBox.Height - 1);
                }
            }
        }

        private void LogoBox_Paint(object sender, PaintEventArgs e)
        {
            // التحقق من أن الأبعاد ليست صفراً
            if (logoBox.Width > 0 && logoBox.Height > 0)
            {
                // رسم شعار بسيط
                using (var brush = new SolidBrush(Color.White))
                {
                    var font = new Font("Tahoma", 24F, FontStyle.Bold);
                    var text = "SA";
                    var size = e.Graphics.MeasureString(text, font);
                    var x = (logoBox.Width - size.Width) / 2;
                    var y = (logoBox.Height - size.Height) / 2;
                    e.Graphics.DrawString(text, font, brush, x, y);
                }
            }
        }

        private void BtnLogin_MouseEnter(object sender, EventArgs e)
        {
            btnLogin.BackColor = Color.FromArgb(28, 151, 234);
        }

        private void BtnLogin_MouseLeave(object sender, EventArgs e)
        {
            btnLogin.BackColor = Color.FromArgb(0, 122, 204);
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnLogin_Click(sender, e);
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (ValidateLogin())
            {
                this.Hide();
                var mainForm = new MainForm();
                mainForm.ShowDialog();
                this.Close();
            }
        }

        private bool ValidateLogin()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            // تسجيل دخول بسيط بدون قاعدة بيانات
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                // حفظ بيانات المستخدم الحالي
                CurrentUser.Instance.Login(1, "admin", "المدير العام", "<EMAIL>", "Admin");
                return true;
            }

            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
}
