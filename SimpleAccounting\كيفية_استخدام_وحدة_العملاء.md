# 📖 دليل استخدام وحدة إدارة العملاء المحسنة

## 🚀 **كيفية الوصول للوحدة:**

1. **تشغيل النظام:**
   ```
   dotnet run --project SimpleAccounting
   ```

2. **تسجيل الدخول:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

3. **الوصول لوحدة العملاء:**
   - اضغط على زر العملاء 👥 في القائمة الجانبية

---

## 🔍 **استخدام البحث السريع:**

### **البحث المباشر (Live Search):**
- اكتب في مربع البحث أي من:
  - اسم العميل
  - رقم الهاتف  
  - البريد الإلكتروني
- **النتائج تظهر فوراً** أثناء الكتابة
- **لا حاجة للضغط على أي زر**

### **مثال على البحث:**
```
البحث بالاسم: "أحمد"
البحث بالهاتف: "050"
البحث بالإيميل: "gmail"
```

---

## ➕ **إضافة عميل جديد:**

1. **اضغط زر "➕ إضافة عميل"**
2. **املأ البيانات المطلوبة:**
   - ✅ **الاسم الكامل** (مطلوب)
   - ✅ **رقم الهاتف** (مطلوب)
   - ⚪ البريد الإلكتروني (اختياري)
   - ⚪ العنوان (اختياري)
3. **اضغط "💾 حفظ العميل"**
4. **ستظهر رسالة تأكيد** وسيتم تحديث القائمة

---

## 👁️ **عرض تفاصيل العميل:**

### **فتح نافذة التفاصيل:**
- **انقر نقراً مزدوجاً** على أي عميل في القائمة

### **ما ستجده في نافذة التفاصيل:**

#### **📋 قسم معلومات العميل:**
- اسم العميل بخط كبير وبارز
- **معلومات سريعة:**
  - إجمالي المشتريات
  - إجمالي الديون
  - تاريخ آخر عملية شراء
- **حقول قابلة للتعديل:**
  - الاسم، الهاتف، البريد، العنوان

#### **📄 قسم فواتير العميل:**
- جدول شامل بجميع فواتير العميل
- **الأعمدة المتوفرة:**
  - رقم الفاتورة
  - التاريخ والوقت
  - المبلغ الإجمالي
  - المبلغ المدفوع
  - المبلغ المتبقي
  - حالة الدفع (مع تلوين)

---

## ✏️ **تعديل بيانات العميل:**

1. **افتح نافذة تفاصيل العميل** (نقر مزدوج)
2. **عدّل البيانات المطلوبة** في الحقول
3. **اضغط "💾 حفظ التغييرات"**
4. **ستظهر رسالة تأكيد** وسيتم تحديث البيانات

---

## 🎨 **فهم الألوان في القائمة:**

### **تلوين الرصيد:**
- 🟢 **أخضر فاتح:** رصيد موجب (العميل له رصيد)
- 🔴 **أحمر فاتح:** ديون (العميل عليه مبلغ)
- ⚪ **رمادي فاتح:** رصيد صفر

### **تلوين حالة الدفع (في نافذة التفاصيل):**
- 🟢 **أخضر:** فاتورة مدفوعة
- 🔴 **أحمر:** فاتورة غير مدفوعة

---

## 🔄 **تحديث القائمة:**

### **طرق التحديث:**
1. **زر التحديث:** اضغط "🔄 تحديث"
2. **تحديث تلقائي:** بعد إضافة أو تعديل عميل
3. **مسح البحث:** زر التحديث يمسح مربع البحث أيضاً

---

## 📊 **فهم أعمدة قائمة العملاء:**

| العمود | الوصف | التنسيق |
|--------|--------|---------|
| 👤 **اسم العميل** | الاسم الكامل | خط عريض، محاذاة يسار |
| 📱 **رقم الهاتف** | رقم الهاتف | لون أزرق، محاذاة وسط |
| 📧 **البريد الإلكتروني** | عنوان الإيميل | لون بنفسجي، محاذاة يسار |
| 💰 **الرصيد** | الرصيد الحالي | تلوين حسب الحالة، تنسيق رقمي |
| 📅 **آخر فاتورة** | تاريخ آخر فاتورة | رمادي، محاذاة وسط |

---

## ⚡ **نصائح للاستخدام الأمثل:**

### **🔍 للبحث السريع:**
- استخدم أجزاء من الاسم أو الرقم
- البحث غير حساس لحالة الأحرف
- يمكن البحث بأي جزء من البيانات

### **📝 لإدخال البيانات:**
- الاسم ورقم الهاتف مطلوبان
- باقي الحقول اختيارية
- يمكن ترك البريد والعنوان فارغين

### **👁️ لعرض التفاصيل:**
- النقر المزدوج أسرع من القوائم
- يمكن تعديل البيانات مباشرة
- الحفظ فوري ومؤكد

### **🎨 لفهم الحالات:**
- راقب ألوان الرصيد للمتابعة
- الأخضر = عميل جيد
- الأحمر = يحتاج متابعة

---

## 🚨 **حل المشاكل الشائعة:**

### **❌ لا تظهر النتائج في البحث:**
- تأكد من كتابة البيانات بشكل صحيح
- جرب البحث بجزء من الاسم فقط
- اضغط زر التحديث لإعادة تحميل البيانات

### **❌ لا تفتح نافذة التفاصيل:**
- تأكد من النقر المزدوج وليس المفرد
- تأكد من النقر على صف وليس على رأس الجدول
- جرب النقر على اسم العميل تحديداً

### **❌ لا يتم حفظ التعديلات:**
- تأكد من ملء الاسم (مطلوب)
- اضغط زر الحفظ وانتظر رسالة التأكيد
- تحقق من أن البيانات تم تحديثها في القائمة

---

## 🎯 **الخلاصة:**

**وحدة إدارة العملاء الآن تتميز بـ:**
- ✅ بحث سريع ومباشر
- ✅ واجهة حديثة وجذابة  
- ✅ تفاصيل شاملة للعملاء
- ✅ تعديل سهل ومباشر
- ✅ تلوين ذكي للبيانات
- ✅ أداء سريع وموثوق

**استمتع بالاستخدام! 🎉**
