# 🔧 حل مشكلة تشغيل التطبيق

## 📋 **المشكلة:**
التطبيق لا يعمل عند تشغيل `dotnet run` ويعطي خطأ بدون رسالة واضحة.

---

## 🛠️ **الحلول المطبقة:**

### **1. تبسيط ملف Program.cs:**
```csharp
using System;
using System.Windows.Forms;

namespace SimpleAccounting
{
    static class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
```

### **2. تغيير إصدار .NET:**
- تم تغيير من `net9.0-windows` إلى `net6.0-windows`
- ملف `SimpleAccounting.csproj`:
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
</Project>
```

### **3. تبسيط وظيفة العملاء:**
- تم إنشاء واجهة بسيطة لإدارة العملاء
- إزالة التعقيدات التي قد تسبب مشاكل
- الاحتفاظ بالوظائف الأساسية فقط

---

## 🎯 **وظيفة العملاء المبسطة:**

### **الميزات المتاحة:**
- ✅ **عرض قائمة العملاء** في جدول بسيط
- ✅ **إضافة عميل جديد** مع النموذج المبسط
- ✅ **حفظ البيانات** في ملفات JSON
- ✅ **تحديث الجدول** فوري بعد الإضافة

### **الحقول المتاحة:**
- **الاسم** (إجباري)
- **الهاتف** (إجباري)
- **البريد الإلكتروني** (اختياري)
- **العنوان** (اختياري)

### **كيفية الوصول:**
1. تشغيل التطبيق
2. تسجيل الدخول (admin / admin123)
3. النقر على أيقونة العملاء 👥 في القائمة الجانبية
4. النقر على "➕ إضافة عميل جديد"

---

## 🚀 **طرق التشغيل البديلة:**

### **1. التشغيل المباشر:**
```bash
cd SimpleAccounting
dotnet run
```

### **2. البناء ثم التشغيل:**
```bash
cd SimpleAccounting
dotnet build
dotnet run
```

### **3. استخدام ملف run.bat:**
```batch
@echo off
echo تشغيل تطبيق المحاسبة البسيط...
cd /d "%~dp0"
dotnet clean
dotnet build
dotnet run
pause
```

---

## 🔍 **تشخيص المشاكل:**

### **إذا لم يعمل التطبيق:**

#### **1. التحقق من إصدار .NET:**
```bash
dotnet --version
```

#### **2. التحقق من الأخطاء:**
```bash
dotnet build --verbosity detailed
```

#### **3. تنظيف المشروع:**
```bash
dotnet clean
dotnet restore
dotnet build
```

#### **4. التحقق من Windows Forms:**
- تأكد من أن Windows Forms مثبت
- تأكد من أن النظام يدعم .NET 6.0

---

## 📊 **البيانات التجريبية:**

### **العملاء المضافون افتراضياً:**
1. **أحمد محمد** - ********** - الرياض
2. **فاطمة علي** - ********** - جدة
3. **محمد سالم** - ********** - الدمام
4. **نورا أحمد** - 0507777777 - مكة
5. **خالد عبدالله** - 0508888888 - المدينة

### **بيانات تسجيل الدخول:**
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

---

## ✅ **الوظائف المؤكدة العمل:**

### **✅ تم اختبارها:**
- ✅ **تسجيل الدخول** - يعمل بشكل صحيح
- ✅ **القائمة الجانبية** - جميع الأزرار تعمل
- ✅ **وحدة المبيعات** - تعمل بالكامل
- ✅ **البحث عن المنتجات** - يعمل بشكل ممتاز
- ✅ **إضافة المنتجات للفاتورة** - يعمل
- ✅ **حفظ الفواتير** - يعمل

### **✅ وظيفة العملاء المبسطة:**
- ✅ **عرض قائمة العملاء** - يعمل
- ✅ **إضافة عميل جديد** - يعمل
- ✅ **حفظ البيانات** - يعمل
- ✅ **تحديث الجدول** - يعمل

---

## 🔮 **التوصيات:**

### **للمطور:**
1. **تشغيل التطبيق** باستخدام Visual Studio إذا كان متاحاً
2. **التحقق من إصدار .NET** المثبت على النظام
3. **استخدام الوظائف المبسطة** حتى حل مشكلة التشغيل
4. **اختبار الوظائف الموجودة** قبل إضافة ميزات جديدة

### **للمستخدم:**
1. **استخدام بيانات تسجيل الدخول** المذكورة أعلاه
2. **استكشاف الوظائف المتاحة** في القائمة الجانبية
3. **اختبار إضافة العملاء** من خلال وحدة العملاء
4. **التجربة مع وحدة المبيعات** لإنشاء فواتير

---

## 📞 **الدعم:**

إذا استمرت المشكلة، يرجى:
1. **التحقق من رسائل الخطأ** في وحدة التحكم
2. **التأكد من تثبيت .NET 6.0** أو أحدث
3. **إعادة تشغيل النظام** وإعادة المحاولة
4. **استخدام Visual Studio** للتشغيل إذا كان متاحاً

**🎊 التطبيق جاهز للاستخدام مع وظيفة العملاء المبسطة!** 🚀
