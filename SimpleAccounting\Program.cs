using System;
using System.Diagnostics;
using System.Threading;
using System.Windows.Forms;

namespace SimpleAccounting
{
    static class Program
    {
        private static Mutex _mutex;

        /// <summary>
        ///  نقطة البداية الرئيسية للتطبيق.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // التحقق من عدم تشغيل نسخة أخرى من التطبيق
            const string mutexName = "SimpleAccountingMutex";
            _mutex = new Mutex(true, mutexName, out bool createdNew);

            if (!createdNew)
            {
                MessageBox.Show("التطبيق يعمل بالفعل!\n\nيرجى إغلاق النسخة الأخرى أولاً.",
                    "تطبيق قيد التشغيل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // تنظيف أي عمليات سابقة
                CleanupPreviousInstances();

                // تخصيص إعدادات التطبيق مثل DPI العالي أو الخط الافتراضي
                ApplicationConfiguration.Initialize();

                // تطبيق الثيم المظلم إذا كان متاحاً
                Application.SetHighDpiMode(HighDpiMode.SystemAware);
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // بدء التطبيق بنافذة تسجيل الدخول
                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                // تنظيف الموارد
                _mutex?.ReleaseMutex();
                _mutex?.Dispose();
            }
        }

        private static void CleanupPreviousInstances()
        {
            try
            {
                var currentProcess = Process.GetCurrentProcess();
                var processes = Process.GetProcessesByName("SimpleAccounting");

                foreach (var process in processes)
                {
                    if (process.Id != currentProcess.Id)
                    {
                        try
                        {
                            process.Kill();
                            process.WaitForExit(5000);
                        }
                        catch
                        {
                            // تجاهل أخطاء إغلاق العمليات
                        }
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }
    }
}