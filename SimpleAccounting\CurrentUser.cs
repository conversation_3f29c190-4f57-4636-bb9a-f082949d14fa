using System;

namespace SimpleAccounting
{
    /// <summary>
    /// إدارة المستخدم الحالي المسجل في النظام - بدون قاعدة بيانات
    /// </summary>
    public sealed class CurrentUser
    {
        private static CurrentUser _instance;
        private static readonly object _lock = new object();

        private CurrentUser() { }

        public static CurrentUser Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new CurrentUser();
                    }
                }
                return _instance;
            }
        }

        #region Properties

        public int UserId { get; private set; }
        public string Username { get; private set; }
        public string FullName { get; private set; }
        public string Email { get; private set; }
        public string Role { get; private set; }
        public DateTime LoginTime { get; private set; }
        public string SessionToken { get; private set; }
        public bool IsLoggedIn { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        public void Login(int userId, string username, string fullName, string email, string role)
        {
            UserId = userId;
            Username = username;
            FullName = fullName;
            Email = email;
            Role = role;
            LoginTime = DateTime.Now;
            SessionToken = GenerateSessionToken();
            IsLoggedIn = true;
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public void Logout()
        {
            UserId = 0;
            Username = null;
            FullName = null;
            Email = null;
            Role = null;
            LoginTime = DateTime.MinValue;
            SessionToken = null;
            IsLoggedIn = false;
        }

        /// <summary>
        /// التحقق من صلاحية الإدارة
        /// </summary>
        public bool IsAdmin => IsLoggedIn && Role == "Admin";

        /// <summary>
        /// التحقق من صلاحية المحاسبة
        /// </summary>
        public bool IsAccountant => IsLoggedIn && (Role == "Admin" || Role == "Accountant");

        /// <summary>
        /// التحقق من صلاحية أمين الصندوق
        /// </summary>
        public bool IsCashier => IsLoggedIn && (Role == "Admin" || Role == "Accountant" || Role == "Cashier");

        #endregion

        #region Private Methods

        private string GenerateSessionToken()
        {
            return Guid.NewGuid().ToString("N");
        }

        #endregion
    }
}
