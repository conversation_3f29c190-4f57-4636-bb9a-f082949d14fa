using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class CustomerDetailsForm : Form
    {
        private SimpleCustomer _customer;
        private Panel mainPanel;
        private Panel customerInfoPanel;
        private Panel invoicesPanel;
        private DataGridView invoicesGrid;
        private Label lblCustomerName;
        private Label lblTotalPurchases;
        private Label lblTotalDebt;
        private Label lblLastPurchase;
        private TextBox txtName;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtAddress;
        private Button btnSave;
        private Button btnClose;

        public CustomerDetailsForm(int customerId)
        {
            LoadCustomerData(customerId);
            InitializeComponent();
            SetupModernUI();
            LoadCustomerInvoices();
        }

        private void LoadCustomerData(int customerId)
        {
            try
            {
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                _customer = customers.FirstOrDefault(c => c.Id == customerId);
                
                if (_customer == null)
                {
                    MessageBox.Show("لم يتم العثور على العميل!", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الأساسية
            this.Text = $"تفاصيل العميل - {_customer?.Name}";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.BackColor = Color.FromArgb(248, 250, 252);
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.MinimumSize = new Size(1000, 600);

            // اللوحة الرئيسية
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(20)
            };

            // لوحة معلومات العميل
            customerInfoPanel = CreateCustomerInfoPanel();
            customerInfoPanel.Location = new Point(20, 20);
            customerInfoPanel.Size = new Size(mainPanel.Width - 40, 300);

            // لوحة الفواتير
            invoicesPanel = CreateInvoicesPanel();
            invoicesPanel.Location = new Point(20, 340);
            invoicesPanel.Size = new Size(mainPanel.Width - 40, mainPanel.Height - 380);

            mainPanel.Controls.AddRange(new Control[] {
                customerInfoPanel, invoicesPanel
            });

            this.Controls.Add(mainPanel);
            this.ResumeLayout(false);
        }

        private Panel CreateCustomerInfoPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Height = 300
            };

            // تأثير الظل
            panel.Paint += (s, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                
                // رسم ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }
                
                // رسم الخلفية
                using (var backgroundBrush = new SolidBrush(Color.White))
                {
                    graphics.FillRectangle(backgroundBrush, 0, 0, panel.Width - 3, panel.Height - 3);
                }
                
                // رسم حدود
                using (var borderPen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    graphics.DrawRectangle(borderPen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📋 معلومات العميل",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // اسم العميل الكبير
            lblCustomerName = new Label
            {
                Text = _customer?.Name ?? "",
                Font = new Font("Tahoma", 20F, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 60),
                Size = new Size(400, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // معلومات سريعة
            var quickInfoPanel = new Panel
            {
                Location = new Point(450, 60),
                Size = new Size(300, 120),
                BackColor = Color.FromArgb(248, 250, 252)
            };

            lblTotalPurchases = new Label
            {
                Text = "إجمالي المشتريات: 0 ريال",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(10, 10),
                Size = new Size(280, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            lblTotalDebt = new Label
            {
                Text = "إجمالي الديون: 0 ريال",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(10, 40),
                Size = new Size(280, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            lblLastPurchase = new Label
            {
                Text = "آخر عملية شراء: لا توجد",
                Font = new Font("Tahoma", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(149, 165, 166),
                Location = new Point(10, 70),
                Size = new Size(280, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            quickInfoPanel.Controls.AddRange(new Control[] {
                lblTotalPurchases, lblTotalDebt, lblLastPurchase
            });

            // حقول التعديل
            var nameLabel = new Label
            {
                Text = "الاسم:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, 110),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtName = new TextBox
            {
                Text = _customer?.Name ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, 108),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            var phoneLabel = new Label
            {
                Text = "الهاتف:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, 145),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtPhone = new TextBox
            {
                Text = _customer?.Phone ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, 143),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            var emailLabel = new Label
            {
                Text = "البريد:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, 180),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtEmail = new TextBox
            {
                Text = _customer?.Email ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, 178),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            var addressLabel = new Label
            {
                Text = "العنوان:",
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                Location = new Point(20, 215),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtAddress = new TextBox
            {
                Text = _customer?.Address ?? "",
                Font = new Font("Tahoma", 11F),
                Location = new Point(110, 213),
                Size = new Size(200, 25),
                BorderStyle = BorderStyle.FixedSingle
            };

            // أزرار الحفظ والإغلاق
            btnSave = new Button
            {
                Text = "💾 حفظ التغييرات",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(110, 250),
                Size = new Size(150, 35),
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;

            btnClose = new Button
            {
                Text = "❌ إغلاق",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(270, 250),
                Size = new Size(100, 35),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;

            // إضافة الأحداث
            btnSave.Click += BtnSave_Click;
            btnClose.Click += (s, e) => this.Close();

            panel.Controls.AddRange(new Control[] {
                titleLabel, lblCustomerName, quickInfoPanel,
                nameLabel, txtName, phoneLabel, txtPhone,
                emailLabel, txtEmail, addressLabel, txtAddress,
                btnSave, btnClose
            });

            return panel;
        }

        private Panel CreateInvoicesPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White
            };

            // تأثير الظل
            panel.Paint += (s, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, 3, 3, panel.Width - 3, panel.Height - 3);
                }

                using (var backgroundBrush = new SolidBrush(Color.White))
                {
                    graphics.FillRectangle(backgroundBrush, 0, 0, panel.Width - 3, panel.Height - 3);
                }

                using (var borderPen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    graphics.DrawRectangle(borderPen, 0, 0, panel.Width - 1, panel.Height - 1);
                }
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "📄 فواتير العميل",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // جدول الفواتير
            invoicesGrid = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(panel.Width - 40, panel.Height - 80),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                Font = new Font("Tahoma", 10F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(230, 230, 230),
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                RowTemplate = { Height = 35 }
            };

            // تنسيق رأس الجدول
            invoicesGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            invoicesGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            invoicesGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            invoicesGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            invoicesGrid.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            invoicesGrid.DefaultCellStyle.BackColor = Color.White;
            invoicesGrid.DefaultCellStyle.ForeColor = Color.FromArgb(52, 73, 94);
            invoicesGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            invoicesGrid.DefaultCellStyle.SelectionForeColor = Color.White;
            invoicesGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // الصفوف المتناوبة
            invoicesGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 250, 252);

            // إضافة الأعمدة
            invoicesGrid.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            invoicesGrid.Columns.Add("Date", "التاريخ");
            invoicesGrid.Columns.Add("Time", "الوقت");
            invoicesGrid.Columns.Add("Total", "المبلغ الإجمالي");
            invoicesGrid.Columns.Add("Paid", "المبلغ المدفوع");
            invoicesGrid.Columns.Add("Remaining", "المبلغ المتبقي");
            invoicesGrid.Columns.Add("Status", "حالة الدفع");

            // تنسيق الأعمدة
            invoicesGrid.Columns["InvoiceNumber"].Width = 120;
            invoicesGrid.Columns["Date"].Width = 100;
            invoicesGrid.Columns["Time"].Width = 80;
            invoicesGrid.Columns["Total"].Width = 120;
            invoicesGrid.Columns["Paid"].Width = 120;
            invoicesGrid.Columns["Remaining"].Width = 120;
            invoicesGrid.Columns["Status"].Width = 100;

            panel.Controls.AddRange(new Control[] {
                titleLabel, invoicesGrid
            });

            return panel;
        }

        private void LoadCustomerInvoices()
        {
            try
            {
                var invoices = SimpleDataManager.Instance.GetAllInvoices()
                    .Where(i => i.CustomerId == _customer.Id)
                    .OrderByDescending(i => i.Date)
                    .ToList();

                invoicesGrid.Rows.Clear();

                decimal totalPurchases = 0;
                decimal totalDebt = 0;
                DateTime? lastPurchaseDate = null;

                foreach (var invoice in invoices)
                {
                    totalPurchases += invoice.Total;

                    // حساب المبلغ المدفوع والمتبقي (مبسط)
                    var paid = invoice.Status == "مدفوعة" ? invoice.Total : 0;
                    var remaining = invoice.Total - paid;
                    totalDebt += remaining;

                    if (lastPurchaseDate == null || invoice.Date > lastPurchaseDate)
                        lastPurchaseDate = invoice.Date;

                    var rowIndex = invoicesGrid.Rows.Add(
                        invoice.InvoiceNumber,
                        invoice.Date.ToString("dd/MM/yyyy"),
                        invoice.Date.ToString("HH:mm"),
                        invoice.Total.ToString("N2"),
                        paid.ToString("N2"),
                        remaining.ToString("N2"),
                        invoice.Status
                    );

                    // تلوين حسب حالة الدفع
                    var row = invoicesGrid.Rows[rowIndex];
                    if (invoice.Status == "مدفوعة")
                    {
                        row.Cells["Status"].Style.BackColor = Color.FromArgb(212, 237, 218);
                        row.Cells["Status"].Style.ForeColor = Color.FromArgb(21, 87, 36);
                    }
                    else
                    {
                        row.Cells["Status"].Style.BackColor = Color.FromArgb(248, 215, 218);
                        row.Cells["Status"].Style.ForeColor = Color.FromArgb(114, 28, 36);
                    }
                }

                // تحديث المعلومات السريعة
                lblTotalPurchases.Text = $"إجمالي المشتريات: {totalPurchases:N2} ريال";
                lblTotalDebt.Text = $"إجمالي الديون: {totalDebt:N2} ريال";
                lblLastPurchase.Text = lastPurchaseDate.HasValue
                    ? $"آخر عملية شراء: {lastPurchaseDate.Value:dd/MM/yyyy}"
                    : "آخر عملية شراء: لا توجد";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فواتير العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل!", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                // تحديث بيانات العميل
                _customer.Name = txtName.Text.Trim();
                _customer.Phone = txtPhone.Text.Trim();
                _customer.Email = txtEmail.Text.Trim();
                _customer.Address = txtAddress.Text.Trim();

                // حفظ التغييرات
                var customers = SimpleDataManager.Instance.GetAllCustomers();
                var customerIndex = customers.FindIndex(c => c.Id == _customer.Id);
                if (customerIndex >= 0)
                {
                    customers[customerIndex] = _customer;
                    SimpleDataManager.Instance.SaveData("customers", customers);

                    // تحديث العرض
                    lblCustomerName.Text = _customer.Name;
                    this.Text = $"تفاصيل العميل - {_customer.Name}";

                    MessageBox.Show("تم حفظ التغييرات بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupModernUI()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);
        }
    }
}
