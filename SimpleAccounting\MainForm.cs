using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace SimpleAccounting
{
    public partial class MainForm : Form
    {
        private Panel sidePanel;
        private Panel contentPanel;
        private Label titleLabel;

        public MainForm()
        {
            InitializeComponent();
            SetupMainInterface();
            LoadDashboard();
        }

        private void SetupMainInterface()
        {
            // إعداد النافذة الرئيسية
            this.Text = "نظام المحاسبة البسيط";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(248, 250, 252);
            this.Font = new Font("Tahoma", 10F);

            // إنشاء اللوحة الجانبية
            CreateSidePanel();

            // إنشاء لوحة المحتوى
            CreateContentPanel();
        }

        private void CreateSidePanel()
        {
            sidePanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 250,
                BackColor = Color.FromArgb(52, 73, 94)
            };

            // شعار النظام
            var logoLabel = new Label
            {
                Text = "💼 نظام المحاسبة",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 30),
                Size = new Size(200, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // أزرار القائمة
            var dashboardBtn = CreateMenuButton("🏠 الرئيسية", 100, LoadDashboard);
            var customersBtn = CreateMenuButton("👥 العملاء", 150, LoadCustomersModule);
            var productsBtn = CreateMenuButton("📦 المنتجات", 200, LoadProductsModule);
            var salesBtn = CreateMenuButton("💰 المبيعات", 250, LoadSalesModule);
            var reportsBtn = CreateMenuButton("📊 التقارير", 300, LoadReportsModule);

            sidePanel.Controls.AddRange(new Control[] {
                logoLabel, dashboardBtn, customersBtn, productsBtn, salesBtn, reportsBtn
            });

            this.Controls.Add(sidePanel);
        }

        private Button CreateMenuButton(string text, int y, Action clickAction)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(20, y),
                Size = new Size(210, 40),
                TextAlign = ContentAlignment.MiddleLeft,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(46, 204, 113);
            button.Click += (s, e) => clickAction?.Invoke();

            return button;
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 250, 252),
                Padding = new Padding(20)
            };

            this.Controls.Add(contentPanel);
        }

        private void ClearContentPanel()
        {
            contentPanel.Controls.Clear();
        }

        private void LoadDashboard()
        {
            ClearContentPanel();
            ShowSimpleMessage("الرئيسية", "مرحباً بك في نظام المحاسبة البسيط\n\nاختر من القائمة الجانبية للبدء");
        }

        private void LoadCustomersModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("العملاء", "وحدة العملاء قيد التطوير");
        }

        private void LoadProductsModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("المنتجات", "وحدة المنتجات قيد التطوير");
        }

        private void LoadSalesModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("المبيعات", "وحدة المبيعات قيد التطوير");
        }

        private void LoadReportsModule()
        {
            ClearContentPanel();
            ShowSimpleMessage("التقارير", "وحدة التقارير قيد التطوير");
        }

        private void ShowSimpleMessage(string title, string message)
        {
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 24F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var messageLabel = new Label
            {
                Text = message,
                Font = new Font("Tahoma", 14F),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(20, 80),
                Size = new Size(600, 200),
                TextAlign = ContentAlignment.TopLeft
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, messageLabel });
        }
    }
}
